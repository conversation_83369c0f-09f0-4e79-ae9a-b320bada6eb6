/**
 * Pano2VR热点定位与节点切换监听脚本
 *
 * 此脚本用于：
 * 1. 接收来自父页面的定位指令并调用pano2vr的JavaScript API实现热点定位功能
 * 2. 监听全景图节点切换事件，支持多节点全景图功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 * @version 2.0 - 增加多节点支持
 */

(function() {
    'use strict';

    // 全局变量
    var currentNodeId = null; // 当前节点ID
    var isNodeSwitchListenerInitialized = false; // 节点切换监听器是否已初始化

    // 等待pano2vr加载完成
    var waitForPano = function(callback) {
        var checkInterval = setInterval(function() {
            // 检查pano对象是否存在且已初始化
            if (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {
                clearInterval(checkInterval);
                callback();
            }
        }, 100); // 每100ms检查一次

        // 10秒超时
        setTimeout(function() {
            clearInterval(checkInterval);
        }, 10000);
    };

    // 获取当前节点ID
    var getCurrentNodeId = function() {
        try {
            // 尝试多种方法获取当前节点ID
            if (typeof pano !== 'undefined' && pano) {
                // 方法1: 直接获取当前节点ID（如果Pano2VR提供此API）
                if (typeof pano.getCurrentNode === 'function') {
                    return pano.getCurrentNode();
                }

                // 方法2: 通过URL参数获取
                if (typeof pano.getURL === 'function') {
                    var url = pano.getURL();
                    var match = url.match(/node=([^&]+)/);
                    if (match) {
                        return match[1];
                    }
                }

                // 方法3: 通过变量获取（如果Pano2VR设置了全局变量）
                if (typeof window.currentNode !== 'undefined') {
                    return window.currentNode;
                }

                // 方法4: 检查DOM中的节点信息
                var nodeElements = document.querySelectorAll('[data-node-id]');
                if (nodeElements.length > 0) {
                    return nodeElements[0].getAttribute('data-node-id');
                }
            }

            // 默认返回node1
            return 'node1';
        } catch (error) {
            console.warn('获取当前节点ID失败:', error);
            return 'node1';
        }
    };

    // 初始化节点切换监听器
    var initNodeSwitchListener = function() {
        if (isNodeSwitchListenerInitialized) {
            return; // 避免重复初始化
        }

        try {
            // 获取初始节点ID
            currentNodeId = getCurrentNodeId();
            console.log('初始节点ID:', currentNodeId);

            // 向父页面发送初始节点信息
            notifyParentNodeSwitch(currentNodeId, 'initial');

            // 方法1: 监听Pano2VR的节点切换事件（如果提供）
            if (typeof pano !== 'undefined' && pano && typeof pano.addEventListener === 'function') {
                pano.addEventListener('nodechange', function(event) {
                    handleNodeSwitch(event.nodeId || getCurrentNodeId());
                });
            }

            // 方法2: 定期检查节点变化（轮询方式）
            var lastNodeId = currentNodeId;
            var nodeCheckInterval = setInterval(function() {
                var newNodeId = getCurrentNodeId();
                if (newNodeId !== lastNodeId) {
                    handleNodeSwitch(newNodeId);
                    lastNodeId = newNodeId;
                }
            }, 500); // 每500ms检查一次

            // 方法3: 监听URL变化（如果节点切换会改变URL）
            var lastUrl = window.location.href;
            var urlCheckInterval = setInterval(function() {
                var currentUrl = window.location.href;
                if (currentUrl !== lastUrl) {
                    lastUrl = currentUrl;
                    var newNodeId = getCurrentNodeId();
                    if (newNodeId !== currentNodeId) {
                        handleNodeSwitch(newNodeId);
                    }
                }
            }, 300); // 每300ms检查一次

            isNodeSwitchListenerInitialized = true;
            console.log('节点切换监听器初始化完成');

        } catch (error) {
            console.error('初始化节点切换监听器失败:', error);
        }
    };

    // 处理节点切换
    var handleNodeSwitch = function(newNodeId) {
        if (newNodeId && newNodeId !== currentNodeId) {
            console.log('检测到节点切换:', currentNodeId, '->', newNodeId);
            currentNodeId = newNodeId;
            notifyParentNodeSwitch(newNodeId, 'switch');
        }
    };

    // 向父页面发送节点切换消息
    var notifyParentNodeSwitch = function(nodeId, type) {
        try {
            var message = {
                type: 'nodeSwitch',
                nodeId: nodeId,
                switchType: type, // 'initial' 或 'switch'
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');
            console.log('已发送节点切换消息:', message);
        } catch (error) {
            console.error('发送节点切换消息失败:', error);
        }
    };

    // 初始化消息监听器
    var initMessageListener = function() {
        window.addEventListener('message', function(event) {
            // 安全检查：验证消息来源（可根据需要调整）
            // if (event.origin !== window.location.origin) return;
            
            var data = event.data;
            if (!data || typeof data !== 'object') return;
            
            // 处理热点定位消息
            if (data.type === 'locateHotspot') {
                handleHotspotLocation(data);
            }
        }, false);
    };
    
    // 处理热点定位
    var handleHotspotLocation = function(data) {
        try {
            var pan = parseFloat(data.pan);
            var tilt = parseFloat(data.tilt);
            var speed = parseFloat(data.speed) || 2.0; // 默认速度
            
            // 验证数据
            if (isNaN(pan) || isNaN(tilt)) {
                return;
            }
            
            // 获取当前FOV，保持不变
            var currentFov = pano.getFov();
            
            // 使用moveTo方法实现平滑过渡
            // moveTo(pan, tilt, fov, speed, roll, projection)
            pano.moveTo(pan, tilt, currentFov, speed);
            
            // 向父页面发送定位完成消息
            setTimeout(function() {
                try {
                    window.parent.postMessage({
                        type: 'hotspotLocationComplete',
                        pan: pan,
                        tilt: tilt,
                        success: true
                    }, '*');
                } catch (e) {
                    // 静默处理错误
                }
            }, speed * 1000 + 100); // 等待动画完成后发送消息
            
        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotLocationComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };
    

    
    // 初始化所有功能
    var initAllFeatures = function() {
        initMessageListener();
        initNodeSwitchListener();
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            waitForPano(initAllFeatures);
        });
    } else {
        waitForPano(initAllFeatures);
    }

    // 为了兼容性，也在window.onload时尝试初始化
    var originalOnload = window.onload;
    window.onload = function() {
        if (originalOnload) originalOnload();
        waitForPano(initAllFeatures);
    };
    
})();
