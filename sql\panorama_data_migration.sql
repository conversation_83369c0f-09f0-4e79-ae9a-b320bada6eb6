-- =====================================================
-- 全景图热点编辑系统数据迁移脚本
-- 创建时间: 2025-06-06 15:06:53
-- 创建者: wanghq
-- 版本: v1.0
-- 数据库: Oracle 11g
-- 描述: 为现有热点数据填充PANORAMA_ID字段
-- 前置条件: 必须先执行 panorama_hotspot_upgrade.sql
-- =====================================================

-- 设置脚本执行环境
SET ECHO ON;
SET FEEDBACK ON;
SET SERVEROUTPUT ON SIZE 1000000;

-- 记录迁移开始时间
PROMPT ========================================
PROMPT 开始执行全景图热点数据迁移
PROMPT 迁移时间: 2025-06-06 15:06:53
PROMPT ========================================

-- 1. 检查前置条件
PROMPT 检查PANORAMA_ID字段是否存在...
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM USER_TAB_COLUMNS 
    WHERE TABLE_NAME = 'PANORAMA_HOTSPOT' 
    AND COLUMN_NAME = 'PANORAMA_ID';
    
    IF v_count = 0 THEN
        RAISE_APPLICATION_ERROR(-20001, '错误：PANORAMA_ID字段不存在，请先执行 panorama_hotspot_upgrade.sql');
    ELSE
        DBMS_OUTPUT.PUT_LINE('✅ PANORAMA_ID字段存在，可以继续迁移');
    END IF;
END;
/

-- 2. 统计迁移前数据状态
PROMPT 统计迁移前数据状态...
SELECT 
    COUNT(*) AS TOTAL_HOTSPOTS,
    COUNT(CASE WHEN PANORAMA_ID IS NULL THEN 1 END) AS NULL_PANORAMA_ID,
    COUNT(CASE WHEN PANORAMA_ID IS NOT NULL THEN 1 END) AS NOT_NULL_PANORAMA_ID,
    COUNT(DISTINCT TASK_ID) AS TOTAL_TASKS
FROM PANORAMA_HOTSPOT;

-- 3. 创建数据迁移日志表（临时）
PROMPT 创建数据迁移日志表...
CREATE TABLE TEMP_MIGRATION_LOG (
    LOG_ID NUMBER,
    TASK_ID NUMBER,
    HOTSPOT_ID NUMBER,
    OLD_PANORAMA_ID VARCHAR2(100),
    NEW_PANORAMA_ID VARCHAR2(100),
    MIGRATION_STATUS VARCHAR2(50),
    MIGRATION_METHOD VARCHAR2(100),
    ERROR_MESSAGE VARCHAR2(1000),
    MIGRATION_TIME DATE DEFAULT SYSDATE
);

-- 创建日志表序列
CREATE SEQUENCE SEQ_MIGRATION_LOG START WITH 1 INCREMENT BY 1;

-- 4. 数据迁移主逻辑
PROMPT 开始数据迁移...
DECLARE
    v_total_tasks NUMBER := 0;
    v_processed_tasks NUMBER := 0;
    v_total_hotspots NUMBER := 0;
    v_migrated_hotspots NUMBER := 0;
    v_failed_hotspots NUMBER := 0;
    
    -- 游标：获取所有需要迁移的任务
    CURSOR task_cursor IS
        SELECT DISTINCT t.TASK_ID, t.TASK_NAME, t.EXTRACT_PATH
        FROM PANORAMA_TASK t
        WHERE EXISTS (
            SELECT 1 FROM PANORAMA_HOTSPOT h 
            WHERE h.TASK_ID = t.TASK_ID 
            AND h.PANORAMA_ID IS NULL
        )
        ORDER BY t.TASK_ID;
    
    -- 游标：获取任务下需要迁移的热点
    CURSOR hotspot_cursor(p_task_id NUMBER) IS
        SELECT HOTSPOT_ID, PAN, TILT, HOTSPOT_XML_ID, ORIGINAL_TITLE
        FROM PANORAMA_HOTSPOT
        WHERE TASK_ID = p_task_id
        AND PANORAMA_ID IS NULL
        ORDER BY HOTSPOT_ID;
        
BEGIN
    -- 统计总数
    SELECT COUNT(DISTINCT TASK_ID) INTO v_total_tasks
    FROM PANORAMA_HOTSPOT
    WHERE PANORAMA_ID IS NULL;
    
    SELECT COUNT(*) INTO v_total_hotspots
    FROM PANORAMA_HOTSPOT
    WHERE PANORAMA_ID IS NULL;
    
    DBMS_OUTPUT.PUT_LINE('需要迁移的任务数: ' || v_total_tasks);
    DBMS_OUTPUT.PUT_LINE('需要迁移的热点数: ' || v_total_hotspots);
    DBMS_OUTPUT.PUT_LINE('');
    
    -- 遍历每个任务
    FOR task_rec IN task_cursor LOOP
        v_processed_tasks := v_processed_tasks + 1;
        
        DBMS_OUTPUT.PUT_LINE('处理任务 ' || v_processed_tasks || '/' || v_total_tasks || 
                           ': ID=' || task_rec.TASK_ID || ', 名称=' || task_rec.TASK_NAME);
        
        -- 检查XML文件路径是否存在
        IF task_rec.EXTRACT_PATH IS NULL THEN
            DBMS_OUTPUT.PUT_LINE('  ⚠️  警告：任务 ' || task_rec.TASK_ID || ' 没有XML文件路径，跳过');
            
            -- 记录日志
            FOR hotspot_rec IN hotspot_cursor(task_rec.TASK_ID) LOOP
                INSERT INTO TEMP_MIGRATION_LOG (
                    LOG_ID, TASK_ID, HOTSPOT_ID, OLD_PANORAMA_ID, NEW_PANORAMA_ID,
                    MIGRATION_STATUS, MIGRATION_METHOD, ERROR_MESSAGE
                ) VALUES (
                    SEQ_MIGRATION_LOG.NEXTVAL, task_rec.TASK_ID, hotspot_rec.HOTSPOT_ID,
                    NULL, NULL, 'SKIPPED', 'NO_XML_PATH', '任务没有XML文件路径'
                );
                v_failed_hotspots := v_failed_hotspots + 1;
            END LOOP;
            
            CONTINUE;
        END IF;
        
        -- 为该任务的热点设置默认的PANORAMA_ID
        -- 策略1：如果只有一个节点，使用 'node1' 作为默认值
        -- 策略2：如果有多个节点，使用 'unknown' 标记需要手动处理
        
        DECLARE
            v_hotspot_count NUMBER := 0;
            v_default_panorama_id VARCHAR2(100) := 'node1'; -- 默认节点ID
        BEGIN
            -- 统计该任务下的热点数量
            SELECT COUNT(*) INTO v_hotspot_count
            FROM PANORAMA_HOTSPOT
            WHERE TASK_ID = task_rec.TASK_ID
            AND PANORAMA_ID IS NULL;
            
            -- 如果热点数量较多，可能是多节点场景，标记为需要手动处理
            IF v_hotspot_count > 20 THEN
                v_default_panorama_id := 'unknown_multi_node';
                DBMS_OUTPUT.PUT_LINE('  📋 任务热点数量较多(' || v_hotspot_count || ')，标记为多节点场景');
            ELSE
                DBMS_OUTPUT.PUT_LINE('  ✅ 任务热点数量较少(' || v_hotspot_count || ')，使用默认节点ID');
            END IF;
            
            -- 批量更新该任务下的所有热点
            UPDATE PANORAMA_HOTSPOT 
            SET PANORAMA_ID = v_default_panorama_id,
                UPDATE_TIME = SYSDATE
            WHERE TASK_ID = task_rec.TASK_ID
            AND PANORAMA_ID IS NULL;
            
            v_migrated_hotspots := v_migrated_hotspots + SQL%ROWCOUNT;
            
            -- 记录迁移日志
            FOR hotspot_rec IN hotspot_cursor(task_rec.TASK_ID) LOOP
                INSERT INTO TEMP_MIGRATION_LOG (
                    LOG_ID, TASK_ID, HOTSPOT_ID, OLD_PANORAMA_ID, NEW_PANORAMA_ID,
                    MIGRATION_STATUS, MIGRATION_METHOD, ERROR_MESSAGE
                ) VALUES (
                    SEQ_MIGRATION_LOG.NEXTVAL, task_rec.TASK_ID, hotspot_rec.HOTSPOT_ID,
                    NULL, v_default_panorama_id, 'SUCCESS', 
                    CASE WHEN v_hotspot_count > 20 THEN 'DEFAULT_MULTI_NODE' ELSE 'DEFAULT_SINGLE_NODE' END,
                    NULL
                );
            END LOOP;
            
            DBMS_OUTPUT.PUT_LINE('  ✅ 已更新 ' || v_hotspot_count || ' 个热点的PANORAMA_ID');
            
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('  ❌ 任务 ' || task_rec.TASK_ID || ' 迁移失败: ' || SQLERRM);
                v_failed_hotspots := v_failed_hotspots + v_hotspot_count;
                
                -- 记录错误日志
                INSERT INTO TEMP_MIGRATION_LOG (
                    LOG_ID, TASK_ID, HOTSPOT_ID, OLD_PANORAMA_ID, NEW_PANORAMA_ID,
                    MIGRATION_STATUS, MIGRATION_METHOD, ERROR_MESSAGE
                ) VALUES (
                    SEQ_MIGRATION_LOG.NEXTVAL, task_rec.TASK_ID, NULL,
                    NULL, NULL, 'ERROR', 'BATCH_UPDATE', SQLERRM
                );
        END;
        
        DBMS_OUTPUT.PUT_LINE('');
    END LOOP;
    
    -- 输出迁移总结
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('数据迁移完成总结:');
    DBMS_OUTPUT.PUT_LINE('处理任务数: ' || v_processed_tasks || '/' || v_total_tasks);
    DBMS_OUTPUT.PUT_LINE('成功迁移热点数: ' || v_migrated_hotspots);
    DBMS_OUTPUT.PUT_LINE('失败热点数: ' || v_failed_hotspots);
    DBMS_OUTPUT.PUT_LINE('总热点数: ' || v_total_hotspots);
    DBMS_OUTPUT.PUT_LINE('========================================');
    
END;
/

-- 5. 验证迁移结果
PROMPT 验证迁移结果...
SELECT 
    COUNT(*) AS TOTAL_HOTSPOTS_AFTER,
    COUNT(CASE WHEN PANORAMA_ID IS NULL THEN 1 END) AS NULL_PANORAMA_ID_AFTER,
    COUNT(CASE WHEN PANORAMA_ID IS NOT NULL THEN 1 END) AS NOT_NULL_PANORAMA_ID_AFTER,
    COUNT(CASE WHEN PANORAMA_ID = 'node1' THEN 1 END) AS DEFAULT_SINGLE_NODE,
    COUNT(CASE WHEN PANORAMA_ID = 'unknown_multi_node' THEN 1 END) AS MULTI_NODE_UNKNOWN
FROM PANORAMA_HOTSPOT;

-- 6. 显示迁移日志统计
PROMPT 迁移日志统计...
SELECT 
    MIGRATION_STATUS,
    MIGRATION_METHOD,
    COUNT(*) AS COUNT
FROM TEMP_MIGRATION_LOG
GROUP BY MIGRATION_STATUS, MIGRATION_METHOD
ORDER BY MIGRATION_STATUS, MIGRATION_METHOD;

-- 7. 显示需要手动处理的任务
PROMPT 需要手动处理的多节点任务...
SELECT DISTINCT 
    t.TASK_ID,
    t.TASK_NAME,
    COUNT(h.HOTSPOT_ID) AS HOTSPOT_COUNT
FROM PANORAMA_TASK t
JOIN PANORAMA_HOTSPOT h ON t.TASK_ID = h.TASK_ID
WHERE h.PANORAMA_ID = 'unknown_multi_node'
GROUP BY t.TASK_ID, t.TASK_NAME
ORDER BY HOTSPOT_COUNT DESC;

-- 8. 提交事务
PROMPT 提交数据库事务...
COMMIT;

-- 记录迁移完成时间
PROMPT ========================================
PROMPT 全景图热点数据迁移完成
PROMPT 完成时间: 2025-06-06 15:06:53
PROMPT ========================================

PROMPT 迁移成功！现有热点数据已填充PANORAMA_ID字段。
PROMPT 
PROMPT 迁移策略说明:
PROMPT 1. 热点数量≤20的任务: 设置为 'node1' (假设为单节点)
PROMPT 2. 热点数量>20的任务: 设置为 'unknown_multi_node' (需要手动处理)
PROMPT 3. 无XML路径的任务: 跳过迁移，状态为 'SKIPPED'
PROMPT 
PROMPT 后续操作建议:
PROMPT 1. 检查标记为 'unknown_multi_node' 的任务，手动分配正确的节点ID
PROMPT 2. 重新上传全景图文件以获得准确的节点关联
PROMPT 3. 执行 DROP TABLE TEMP_MIGRATION_LOG; 清理临时日志表
PROMPT 
PROMPT 查看详细迁移日志: SELECT * FROM TEMP_MIGRATION_LOG ORDER BY LOG_ID;

-- 脚本执行完成
EXIT;
