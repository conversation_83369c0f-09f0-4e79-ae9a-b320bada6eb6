-- =====================================================
-- 全景图热点编辑系统多节点支持数据库升级脚本
-- 创建时间: 2025-06-06 14:54:40
-- 创建者: wanghq
-- 版本: v1.0
-- 数据库: Oracle 11g
-- 描述: 为PANORAMA_HOTSPOT表添加PANORAMA_ID字段以支持多节点功能
-- =====================================================

-- 设置脚本执行环境
SET ECHO ON;
SET FEEDBACK ON;
SET SERVEROUTPUT ON;

-- 记录升级开始时间
PROMPT ========================================
PROMPT 开始执行全景图热点编辑系统多节点支持升级
PROMPT 升级时间: 2025-06-06 14:54:40
PROMPT ========================================

-- 1. 检查表是否存在
PROMPT 检查PANORAMA_HOTSPOT表是否存在...
SELECT COUNT(*) AS TABLE_EXISTS 
FROM USER_TABLES 
WHERE TABLE_NAME = 'PANORAMA_HOTSPOT';

-- 2. 检查PANORAMA_ID字段是否已存在
PROMPT 检查PANORAMA_ID字段是否已存在...
SELECT COUNT(*) AS COLUMN_EXISTS 
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = 'PANORAMA_HOTSPOT' 
AND COLUMN_NAME = 'PANORAMA_ID';

-- 3. 备份现有数据统计信息
PROMPT 备份现有数据统计信息...
SELECT 
    COUNT(*) AS TOTAL_HOTSPOTS,
    COUNT(DISTINCT TASK_ID) AS TOTAL_TASKS,
    MIN(CREATE_TIME) AS EARLIEST_RECORD,
    MAX(CREATE_TIME) AS LATEST_RECORD
FROM PANORAMA_HOTSPOT;

-- 4. 添加PANORAMA_ID字段
PROMPT 为PANORAMA_HOTSPOT表添加PANORAMA_ID字段...
ALTER TABLE PANORAMA_HOTSPOT 
ADD PANORAMA_ID VARCHAR2(100);

-- 5. 添加字段注释
PROMPT 添加PANORAMA_ID字段注释...
COMMENT ON COLUMN PANORAMA_HOTSPOT.PANORAMA_ID IS '所属全景节点ID，用于多节点支持功能';

-- 6. 创建索引以提升查询性能
PROMPT 为PANORAMA_ID字段创建索引...
CREATE INDEX IDX_HOTSPOT_PANORAMA_ID ON PANORAMA_HOTSPOT(PANORAMA_ID);

-- 7. 创建复合索引以优化常用查询
PROMPT 创建TASK_ID和PANORAMA_ID的复合索引...
CREATE INDEX IDX_HOTSPOT_TASK_PANORAMA ON PANORAMA_HOTSPOT(TASK_ID, PANORAMA_ID);

-- 8. 验证字段添加成功
PROMPT 验证PANORAMA_ID字段添加成功...
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    NULLABLE,
    DATA_DEFAULT
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = 'PANORAMA_HOTSPOT' 
AND COLUMN_NAME = 'PANORAMA_ID';

-- 9. 验证索引创建成功
PROMPT 验证索引创建成功...
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    COLUMN_POSITION
FROM USER_IND_COLUMNS 
WHERE TABLE_NAME = 'PANORAMA_HOTSPOT' 
AND INDEX_NAME IN ('IDX_HOTSPOT_PANORAMA_ID', 'IDX_HOTSPOT_TASK_PANORAMA')
ORDER BY INDEX_NAME, COLUMN_POSITION;

-- 10. 检查表结构完整性
PROMPT 检查表结构完整性...
SELECT 
    COUNT(*) AS TOTAL_COLUMNS
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = 'PANORAMA_HOTSPOT';

-- 11. 验证现有数据完整性
PROMPT 验证现有数据完整性...
SELECT 
    COUNT(*) AS TOTAL_RECORDS_AFTER_UPGRADE,
    COUNT(CASE WHEN PANORAMA_ID IS NULL THEN 1 END) AS NULL_PANORAMA_ID_COUNT,
    COUNT(CASE WHEN PANORAMA_ID IS NOT NULL THEN 1 END) AS NOT_NULL_PANORAMA_ID_COUNT
FROM PANORAMA_HOTSPOT;

-- 12. 提交事务
PROMPT 提交数据库事务...
COMMIT;

-- 记录升级完成时间
PROMPT ========================================
PROMPT 全景图热点编辑系统多节点支持升级完成
PROMPT 完成时间: 2025-06-06 14:54:40
PROMPT ========================================

-- 升级成功提示
PROMPT 升级成功！PANORAMA_HOTSPOT表已成功添加PANORAMA_ID字段。
PROMPT 
PROMPT 升级内容总结:
PROMPT 1. 添加PANORAMA_ID字段 (VARCHAR2(100), 允许NULL)
PROMPT 2. 添加字段注释
PROMPT 3. 创建单列索引: IDX_HOTSPOT_PANORAMA_ID
PROMPT 4. 创建复合索引: IDX_HOTSPOT_TASK_PANORAMA
PROMPT 5. 验证数据完整性
PROMPT 
PROMPT 注意事项:
PROMPT - 现有热点数据的PANORAMA_ID字段为NULL，这是正常的
PROMPT - 新上传的全景图数据将自动填充PANORAMA_ID字段
PROMPT - 如需为现有数据填充PANORAMA_ID，请执行数据迁移脚本
PROMPT 
PROMPT 下一步: 执行数据迁移脚本 panorama_data_migration.sql (可选)

-- 脚本执行完成
EXIT;
