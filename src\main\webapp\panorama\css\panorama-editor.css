/* 全景图热点编辑系统样式 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 确保html元素也不显示滚动条 */
html {
    overflow: hidden !important;
    height: 100%;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    overflow: hidden !important; /* 隐藏页面级别的滚动条 */
    height: 100%; /* 确保body高度为100% */
}

/* 顶部导航栏 */
.panorama-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
}

.header-left .system-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-left .system-title i {
    font-size: 24px;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 400px;
    margin: 0 40px;
}

.task-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
}

.task-selector label {
    font-weight: 500;
    white-space: nowrap;
}

.task-selector .layui-form-item {
    margin-bottom: 0;
}

.task-selector .layui-input-inline {
    margin-right: 0;
}

/* Layui select 组件样式自定义 */
.task-selector .layui-form-select {
    min-width: 250px;
}

/* 选择框标题区域 */
.task-selector .layui-form-select .layui-select-title {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    border-radius: 4px;
}

/* 选择框输入框 */
.task-selector .layui-form-select .layui-select-title input {
    color: white !important;
    background: transparent !important;
}

/* 选择框占位符 */
.task-selector .layui-form-select .layui-select-title input::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* 下拉箭头 */
.task-selector .layui-form-select .layui-edge {
    border-top-color: rgba(255, 255, 255, 0.8) !important;
}

/* 下拉选项容器 */
.task-selector .layui-form-select dl {
    background-color: white !important;
    border: 1px solid #e6e8eb !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 下拉选项 */
.task-selector .layui-form-select dl dd {
    color: #333 !important;
    background-color: white !important;
}

/* 下拉选项悬停 */
.task-selector .layui-form-select dl dd:hover {
    background-color: #f8f9fa !important;
    color: #667eea !important;
}

/* 下拉选项选中 */
.task-selector .layui-form-select dl dd.layui-this {
    background-color: #667eea !important;
    color: white !important;
}

/* 选择框获得焦点时 */
.task-selector .layui-form-select.layui-form-selected .layui-select-title {
    border-color: rgba(255, 255, 255, 0.6) !important;
}

.header-right {
    display: flex;
    gap: 10px;
}

.panorama-container {
    margin-top: 60px;
    height: calc(100vh - 60px); /* 使用精确高度而非最小高度 */
    overflow: hidden; /* 确保容器本身不产生滚动条 */
}

.container-content {
    display: flex;
    height: 100%; /* 使用100%而非calc，避免重复计算 */
    position: relative;
    overflow: hidden; /* 确保内容容器不产生滚动条 */
}

/* 左侧面板 - 可调整宽度 */
.left-panel {
    width: 30%;
    min-width: 300px;
    max-width: 60%;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* 可拖拽分隔条 */
.resize-handle {
    width: 6px;
    background: #f0f0f0;
    cursor: col-resize;
    position: relative;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.resize-handle:hover {
    background: #d0d0d0;
}

.resize-handle::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 30px;
    background: #999;
    border-radius: 1px;
}

.resize-handle:hover::before {
    background: #666;
}

/* 右侧面板 - 自适应宽度 */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    min-width: 300px;
}

/* 卡片样式 */
.panel-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.panel-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: #fafbfc;
    border-bottom: 1px solid #e6e8eb;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header h3 i {
    color: #667eea;
}

.card-tools {
    display: flex;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

/* 任务信息和文件上传合并区域 */
.task-upload-combined {
    flex-shrink: 0;
    height: 180px;
    border-bottom: 1px solid #e6e8eb;
}

.task-upload-combined .card-header {
    padding: 12px 16px;
    background: #fafbfc;
}

.task-upload-combined .card-body {
    padding: 16px;
    height: calc(100% - 45px);
    overflow-y: auto;
}

/* 任务信息区域 - 两行两列布局 */
.task-info-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.task-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px 16px;
}

.task-info .info-item {
    display: flex;
    align-items: flex-start;
    font-size: 13px;
}

.task-info .info-label {
    font-weight: 500;
    color: #666;
    min-width: 70px;
    flex-shrink: 0;
}

.task-info .info-value {
    color: #333;
    word-break: break-all;
    flex: 1;
}

/* 文件上传区域 - 一行显示 */
.upload-section {
    margin-top: 12px;
    display: flex;
    gap: 16px;
}

.upload-section .upload-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    flex: 1;
}

.upload-label {
    font-weight: 500;
    color: #666;
    font-size: 12px;
    white-space: nowrap;
}

.upload-controls {
    display: flex;
    align-items: center;
    gap: 6px;
}

.upload-status {
    font-size: 11px;
    color: #999;
    white-space: nowrap;
}

/* 紧凑按钮样式 */
.upload-controls .layui-btn {
    padding: 4px 10px;
    font-size: 12px;
    height: 26px;
    line-height: 18px;
}

/* 表格卡片 - 占据剩余高度，分页栏在底部 */
.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
    overflow: hidden;
}

.table-card .card-header {
    padding: 12px 16px;
    background: #fafbfc;
    flex-shrink: 0;
}

.table-card .card-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden !important; /* 强制隐藏容器滚动条 */
}

.table-container .layui-table-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100% !important;
    overflow: hidden !important; /* 确保表格视图容器不显示滚动条 */
}

.table-container .layui-table-box {
    flex: 1;
}

.table-container .layui-table-body {
    height: 100% !important;
    overflow: auto !important; /* 确保表格主体支持双向滚动 */
}

.table-container .layui-table-page {
    flex-shrink: 0;
    border-top: 1px solid #e6e8eb;
    background: #fafbfc;
}

/* 预览卡片 - 全高度显示 */
.preview-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.preview-card .card-header {
    padding: 12px 16px;
    background: #fafbfc;
    flex-shrink: 0;
}

.preview-card .card-body {
    flex: 1;
    padding: 0;
    position: relative;
    height: calc(100% - 45px);
}

.preview-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #f8f9fa;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 14px;
}

.preview-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #ddd;
}

.panorama-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container-content {
        grid-template-columns: 400px 1fr;
    }
}

@media (max-width: 992px) {
    .container-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        height: auto;
    }
    
    .left-panel {
        order: 1;
    }
    
    .right-panel {
        order: 2;
        min-height: 500px;
    }
    
    .header-content {
        flex-direction: column;
        height: auto;
        padding: 10px 20px;
    }
    
    .header-center {
        margin: 10px 0;
        max-width: none;
    }
    
    .panorama-header {
        height: auto;
    }
    
    .panorama-container {
        margin-top: 120px;
    }
}

@media (max-width: 768px) {
    .panorama-container {
        padding: 10px;
    }
    
    .container-content {
        gap: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .upload-section .upload-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .upload-label {
        min-width: auto;
    }
}

.layui-table-cell {
    height: 28px !important; /* 固定较小的行高 */
    padding: 3px 6px !important; /* 进一步减少内边距 */
    line-height: 1.2 !important; /* 更紧凑的行高 */
    font-size: 13px !important; /* 稍微减小字体以适应紧凑设计 */
}

.layui-table th {
    background-color: #fafbfc !important;
    font-weight: 600 !important;
    padding: 5px 6px !important; /* 表头也使用紧凑样式 */
    height: 32px !important; /* 表头稍高一点 */
    font-size: 13px !important;
}

.layui-table td {
    padding: 3px 6px !important; /* 数据行使用最紧凑样式 */
    height: 28px !important;
}

.table-container .layui-table-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container .layui-table-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container .layui-table-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-container .layui-table-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.table-container .layui-table-body::-webkit-scrollbar-corner {
    background: #f1f1f1;
}

.layui-table tbody tr td .layui-btn-xs {
    padding: 2px 6px !important; /* 减少按钮内边距 */
    font-size: 11px !important; /* 减小字体 */
    height: 22px !important; /* 固定按钮高度 */
    line-height: 18px !important; /* 调整行高 */
    margin: 1px 2px !important; /* 减少按钮间距 */
    border-radius: 2px !important; /* 小圆角 */
}

/* 操作列容器优化 */
.layui-table tbody tr td[data-field=""] {
    padding: 2px 4px !important; /* 操作列使用更小的内边距 */
}

.task-selection-mask {
    position: absolute;
    top: 0; 
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85); /* 半透明白色背景 */
    backdrop-filter: blur(5px); /* 背景模糊效果 */
    -webkit-backdrop-filter: blur(5px); /* Safari兼容 */
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: maskFadeIn 0.3s ease-in-out;
}

.mask-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mask-icon {
    margin-bottom: 20px;
}

.mask-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.4;
}

.mask-message {
    font-size: 14px;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.mask-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.mask-actions .layui-btn {
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
    text-align: center !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
}

/* 蒙版淡入动画 */
@keyframes maskFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 蒙版淡出动画 */
@keyframes maskFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

.task-selection-mask.fade-out {
    animation: maskFadeOut 0.3s ease-in-out forwards;
}

.layui-form-label{
    width: 90px !important;
}

/* 对话框样式 */
.layui-layer-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* 按钮样式优化 */
.layui-btn {
    border-radius: 4px;
    font-weight: 500;
}

.layui-btn-normal {
    background-color: #667eea;
    border-color: #667eea;
}

.layui-btn-normal:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

.layui-badge {
    border-radius: 8px !important;
    font-size: 10px !important; /* 更小的字体 */
    padding: 1px 4px !important; /* 更小的内边距 */
    line-height: 1.2 !important;
    display: inline-block;
    white-space: nowrap; /* 防止文字换行 */
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar {
    width: 6px;
}

.left-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
