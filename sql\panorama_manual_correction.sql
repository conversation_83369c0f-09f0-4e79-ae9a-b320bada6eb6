-- =====================================================
-- 全景图热点编辑系统手动修正脚本
-- 创建时间: 2025-06-06 15:06:53
-- 创建者: wanghq
-- 版本: v1.0
-- 数据库: Oracle 11g
-- 描述: 手动修正无法自动迁移的热点PANORAMA_ID
-- 前置条件: 必须先执行 panorama_data_migration.sql
-- =====================================================

-- 设置脚本执行环境
SET ECHO ON;
SET FEEDBACK ON;
SET SERVEROUTPUT ON;

PROMPT ========================================
PROMPT 全景图热点PANORAMA_ID手动修正工具
PROMPT 修正时间: 2025-06-06 15:06:53
PROMPT ========================================

-- 1. 查看需要手动处理的任务
PROMPT 1. 查看需要手动处理的任务列表...
SELECT 
    t.TASK_ID,
    t.TASK_NAME,
    t.MODEL_NAME,
    COUNT(h.HOTSPOT_ID) AS HOTSPOT_COUNT,
    t.EXTRACT_PATH
FROM PANORAMA_TASK t
JOIN PANORAMA_HOTSPOT h ON t.TASK_ID = h.TASK_ID
WHERE h.PANORAMA_ID IN ('unknown_multi_node', 'SKIPPED')
GROUP BY t.TASK_ID, t.TASK_NAME, t.MODEL_NAME, t.EXTRACT_PATH
ORDER BY HOTSPOT_COUNT DESC;

-- 2. 查看具体的热点分布情况
PROMPT 2. 查看热点的坐标分布情况（用于分析节点归属）...
SELECT 
    h.TASK_ID,
    h.PANORAMA_ID,
    h.PAN,
    h.TILT,
    h.ORIGINAL_TITLE,
    h.HOTSPOT_XML_ID
FROM PANORAMA_HOTSPOT h
JOIN PANORAMA_TASK t ON h.TASK_ID = t.TASK_ID
WHERE h.PANORAMA_ID IN ('unknown_multi_node', 'SKIPPED')
ORDER BY h.TASK_ID, h.PAN, h.TILT;

-- 3. 提供手动修正的模板SQL
PROMPT 3. 手动修正模板SQL（请根据实际情况修改）...
PROMPT 
PROMPT -- 示例1：将特定任务的所有热点设置为指定节点
PROMPT -- UPDATE PANORAMA_HOTSPOT 
PROMPT -- SET PANORAMA_ID = 'node1', UPDATE_TIME = SYSDATE 
PROMPT -- WHERE TASK_ID = 1 AND PANORAMA_ID = 'unknown_multi_node';
PROMPT 
PROMPT -- 示例2：根据坐标范围分配节点（需要根据实际XML结构调整）
PROMPT -- UPDATE PANORAMA_HOTSPOT 
PROMPT -- SET PANORAMA_ID = 'node1', UPDATE_TIME = SYSDATE 
PROMPT -- WHERE TASK_ID = 1 AND PAN BETWEEN 0 AND 180 AND PANORAMA_ID = 'unknown_multi_node';
PROMPT 
PROMPT -- UPDATE PANORAMA_HOTSPOT 
PROMPT -- SET PANORAMA_ID = 'node2', UPDATE_TIME = SYSDATE 
PROMPT -- WHERE TASK_ID = 1 AND PAN BETWEEN 181 AND 360 AND PANORAMA_ID = 'unknown_multi_node';
PROMPT 
PROMPT -- 示例3：根据热点标题关键词分配节点
PROMPT -- UPDATE PANORAMA_HOTSPOT 
PROMPT -- SET PANORAMA_ID = 'node1', UPDATE_TIME = SYSDATE 
PROMPT -- WHERE TASK_ID = 1 AND UPPER(ORIGINAL_TITLE) LIKE '%NODE1%' AND PANORAMA_ID = 'unknown_multi_node';
PROMPT 

-- 4. 创建手动修正记录表
PROMPT 4. 创建手动修正记录表...
CREATE TABLE PANORAMA_MANUAL_CORRECTIONS (
    CORRECTION_ID NUMBER,
    TASK_ID NUMBER,
    HOTSPOT_ID NUMBER,
    OLD_PANORAMA_ID VARCHAR2(100),
    NEW_PANORAMA_ID VARCHAR2(100),
    CORRECTION_REASON VARCHAR2(500),
    CORRECTED_BY VARCHAR2(50) DEFAULT 'manual',
    CORRECTION_TIME DATE DEFAULT SYSDATE
);

CREATE SEQUENCE SEQ_MANUAL_CORRECTIONS START WITH 1 INCREMENT BY 1;

-- 5. 创建修正验证函数
PROMPT 5. 创建修正验证存储过程...
CREATE OR REPLACE PROCEDURE VALIDATE_PANORAMA_CORRECTIONS AS
    v_total_hotspots NUMBER;
    v_null_panorama_id NUMBER;
    v_unknown_panorama_id NUMBER;
    v_valid_panorama_id NUMBER;
BEGIN
    -- 统计修正后的数据状态
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN PANORAMA_ID IS NULL THEN 1 END),
        COUNT(CASE WHEN PANORAMA_ID IN ('unknown_multi_node', 'SKIPPED') THEN 1 END),
        COUNT(CASE WHEN PANORAMA_ID NOT IN ('unknown_multi_node', 'SKIPPED') AND PANORAMA_ID IS NOT NULL THEN 1 END)
    INTO v_total_hotspots, v_null_panorama_id, v_unknown_panorama_id, v_valid_panorama_id
    FROM PANORAMA_HOTSPOT;
    
    DBMS_OUTPUT.PUT_LINE('========================================');
    DBMS_OUTPUT.PUT_LINE('PANORAMA_ID修正验证结果:');
    DBMS_OUTPUT.PUT_LINE('总热点数: ' || v_total_hotspots);
    DBMS_OUTPUT.PUT_LINE('NULL值数量: ' || v_null_panorama_id);
    DBMS_OUTPUT.PUT_LINE('待处理数量: ' || v_unknown_panorama_id);
    DBMS_OUTPUT.PUT_LINE('已修正数量: ' || v_valid_panorama_id);
    DBMS_OUTPUT.PUT_LINE('修正完成率: ' || ROUND(v_valid_panorama_id * 100.0 / v_total_hotspots, 2) || '%');
    DBMS_OUTPUT.PUT_LINE('========================================');
    
    -- 如果还有待处理的数据，显示详细信息
    IF v_unknown_panorama_id > 0 THEN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('仍有 ' || v_unknown_panorama_id || ' 个热点需要手动处理：');
        
        FOR rec IN (
            SELECT t.TASK_ID, t.TASK_NAME, COUNT(h.HOTSPOT_ID) AS COUNT
            FROM PANORAMA_TASK t
            JOIN PANORAMA_HOTSPOT h ON t.TASK_ID = h.TASK_ID
            WHERE h.PANORAMA_ID IN ('unknown_multi_node', 'SKIPPED')
            GROUP BY t.TASK_ID, t.TASK_NAME
            ORDER BY COUNT DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE('  任务 ' || rec.TASK_ID || ' (' || rec.TASK_NAME || '): ' || rec.COUNT || ' 个热点');
        END LOOP;
    ELSE
        DBMS_OUTPUT.PUT_LINE('🎉 所有热点的PANORAMA_ID都已正确设置！');
    END IF;
END;
/

-- 6. 创建批量修正存储过程
PROMPT 6. 创建批量修正存储过程...
CREATE OR REPLACE PROCEDURE BATCH_UPDATE_PANORAMA_ID(
    p_task_id IN NUMBER,
    p_new_panorama_id IN VARCHAR2,
    p_reason IN VARCHAR2 DEFAULT '批量手动修正'
) AS
    v_updated_count NUMBER;
BEGIN
    -- 执行批量更新
    UPDATE PANORAMA_HOTSPOT 
    SET PANORAMA_ID = p_new_panorama_id,
        UPDATE_TIME = SYSDATE
    WHERE TASK_ID = p_task_id 
    AND PANORAMA_ID IN ('unknown_multi_node', 'SKIPPED');
    
    v_updated_count := SQL%ROWCOUNT;
    
    -- 记录修正日志
    INSERT INTO PANORAMA_MANUAL_CORRECTIONS (
        CORRECTION_ID, TASK_ID, HOTSPOT_ID, OLD_PANORAMA_ID, 
        NEW_PANORAMA_ID, CORRECTION_REASON
    )
    SELECT 
        SEQ_MANUAL_CORRECTIONS.NEXTVAL, p_task_id, HOTSPOT_ID, 
        CASE WHEN PANORAMA_ID IN ('unknown_multi_node', 'SKIPPED') THEN PANORAMA_ID ELSE 'unknown' END,
        p_new_panorama_id, p_reason
    FROM PANORAMA_HOTSPOT
    WHERE TASK_ID = p_task_id AND PANORAMA_ID = p_new_panorama_id;
    
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('✅ 任务 ' || p_task_id || ' 已更新 ' || v_updated_count || ' 个热点的PANORAMA_ID为: ' || p_new_panorama_id);
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        DBMS_OUTPUT.PUT_LINE('❌ 任务 ' || p_task_id || ' 批量更新失败: ' || SQLERRM);
        RAISE;
END;
/

-- 7. 使用示例
PROMPT 7. 使用示例...
PROMPT 
PROMPT -- 示例：将任务1的所有待处理热点设置为node1
PROMPT -- EXEC BATCH_UPDATE_PANORAMA_ID(1, 'node1', '单节点全景图');
PROMPT 
PROMPT -- 示例：将任务2的所有待处理热点设置为node2  
PROMPT -- EXEC BATCH_UPDATE_PANORAMA_ID(2, 'node2', '第二个节点');
PROMPT 
PROMPT -- 验证修正结果
PROMPT -- EXEC VALIDATE_PANORAMA_CORRECTIONS;
PROMPT 

-- 8. 执行初始验证
PROMPT 8. 执行初始验证...
EXEC VALIDATE_PANORAMA_CORRECTIONS;

PROMPT ========================================
PROMPT 手动修正工具准备完成
PROMPT ========================================
PROMPT 
PROMPT 使用说明:
PROMPT 1. 查看上面的任务列表，确定需要修正的任务
PROMPT 2. 根据实际的XML文件结构，确定正确的节点ID
PROMPT 3. 使用 BATCH_UPDATE_PANORAMA_ID 存储过程进行批量修正
PROMPT 4. 使用 VALIDATE_PANORAMA_CORRECTIONS 验证修正结果
PROMPT 5. 查看修正记录: SELECT * FROM PANORAMA_MANUAL_CORRECTIONS;
PROMPT 
PROMPT 注意事项:
PROMPT - 修正前请备份数据
PROMPT - 确保节点ID与实际XML文件中的panorama节点ID一致
PROMPT - 修正后建议重新测试热点定位功能

-- 脚本执行完成
EXIT;
